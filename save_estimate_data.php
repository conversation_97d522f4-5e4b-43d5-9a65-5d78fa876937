<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $projectId = $input['project_id'] ?? null;
    $estimateData = $input['estimate_data'] ?? null;
    
    if (!$projectId || !$estimateData) {
        throw new Exception('Missing required fields: project_id and estimate_data');
    }
    
    require_once 'project_manager.php';
    $projectManager = new ProjectManager();
    
    // Get current project
    $project = $projectManager->getProject($projectId);
    if (!$project) {
        throw new Exception('Project not found');
    }
    
    // Update the estimate data in the project
    if (!isset($project['estimate_data'])) {
        $project['estimate_data'] = [];
    }
    $project['estimate_data']['user_inputs'] = $estimateData;
    $project['updated_at'] = date('Y-m-d H:i:s');

    // Save the updated project
    $success = $projectManager->updateProject($projectId, $project);
    
    if ($success) {
        echo json_encode([
            'success' => true,
            'message' => 'Estimate data saved successfully',
            'timestamp' => $project['updated_at']
        ]);
    } else {
        throw new Exception('Failed to save estimate data');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
