<?php
require_once 'project_manager.php';

$projectId = $_GET['project'] ?? '';
$projectManager = new ProjectManager();
$project = $projectManager->getProject($projectId);

if (!$project) {
    die('Project not found');
}

$data = $project['estimate_data']['data'] ?? [];
$info = $data['info'] ?? [];
$wallCalculations = $info['wall_calculations'] ?? [];
$deductionCalculations = $info['deduction_calculations'] ?? [];

// Generate project title
$title = $projectManager->generateProjectTitle($project);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drawings with Segments - SmartEstimate</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .tabs {
            display: flex;
            background: white;
            border-radius: 10px 10px 0 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            border-right: 1px solid #dee2e6;
        }

        .tab:last-child {
            border-right: none;
        }

        .tab.active {
            background: #007cba;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #e9ecef;
        }

        .tab-content {
            background: white;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 600px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .drawing-container {
            position: relative;
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            overflow: hidden;
            background: #fff;
        }

        .drawing-svg {
            width: 100%;
            height: 100%;
        }

        .controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .control-btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .control-btn:hover {
            background: #005a8b;
        }

        .segment-info {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        .segment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .segment-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .segment-card h4 {
            color: #007cba;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .segment-details {
            font-size: 14px;
            line-height: 1.6;
        }

        .segment-details strong {
            color: #333;
        }

        .wall-segment {
            fill: none;
            stroke: #007cba;
            stroke-width: 2;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .wall-segment:hover {
            stroke: #ff6b35;
            stroke-width: 3;
        }

        .wall-segment.walls-10cm {
            stroke: #28a745;
        }

        .wall-segment.walls-20cm {
            stroke: #007cba;
        }

        .wall-segment.walls-23cm {
            stroke: #6f42c1;
        }

        .deduction-segment {
            fill: rgba(255, 107, 53, 0.3);
            stroke: #ff6b35;
            stroke-width: 1;
            cursor: pointer;
        }

        .legend {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin: 3px 0;
        }

        .legend-color {
            width: 20px;
            height: 3px;
            margin-right: 8px;
            border-radius: 2px;
        }

        .back-link {
            display: inline-block;
            margin: 20px 0;
            padding: 10px 20px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .back-link:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Drawings with Segments</h1>
        <p><?php echo htmlspecialchars($title); ?></p>
    </div>

    <div class="container">
        <a href="cpwd_estimate.php?project=<?php echo urlencode($projectId); ?>" class="back-link">← Back to Estimate</a>

        <div class="tabs">
            <button class="tab active" onclick="showTab('ground-floor')">Ground Floor</button>
            <?php
            // Check for first floor data
            $hasFirstFloor = false;
            foreach ($wallCalculations as $wallType => $data) {
                if (strpos(strtolower($wallType), 'first') !== false ||
                    strpos(strtolower($wallType), '1st') !== false ||
                    strpos(strtolower($wallType), 'ff') !== false) {
                    $hasFirstFloor = true;
                    break;
                }
            }
            if ($hasFirstFloor): ?>
            <button class="tab" onclick="showTab('first-floor')">First Floor</button>
            <?php endif; ?>
            <button class="tab" onclick="showTab('deductions')">Deductions</button>
        </div>

        <!-- Ground Floor Tab -->
        <div id="ground-floor" class="tab-content active">
            <div class="drawing-container">
                <div class="controls">
                    <button class="control-btn" onclick="zoomIn('ground-floor-svg')">Zoom In</button>
                    <button class="control-btn" onclick="zoomOut('ground-floor-svg')">Zoom Out</button>
                    <button class="control-btn" onclick="fitToScreen('ground-floor-svg')">Fit to Screen</button>
                    <button class="control-btn" onclick="resetView('ground-floor-svg')">Reset</button>
                    <br>
                    <button class="control-btn" onclick="panUp('ground-floor-svg')">↑</button>
                    <button class="control-btn" onclick="panDown('ground-floor-svg')">↓</button>
                    <button class="control-btn" onclick="panLeft('ground-floor-svg')">←</button>
                    <button class="control-btn" onclick="panRight('ground-floor-svg')">→</button>
                </div>
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #28a745;"></div>
                        <span>10CM Walls</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #007cba;"></div>
                        <span>20CM Walls</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #6f42c1;"></div>
                        <span>23CM Walls</span>
                    </div>
                </div>
                <svg id="ground-floor-svg" class="drawing-svg" viewBox="0 0 800 600">
                    <?php echo generateFloorSVG($wallCalculations, 'ground'); ?>
                </svg>
            </div>
            <div class="segment-info">
                <h3>Ground Floor Segments</h3>
                <div class="segment-grid">
                    <?php echo generateSegmentCards($wallCalculations); ?>
                </div>
            </div>
        </div>

        <!-- First Floor Tab -->
        <?php if ($hasFirstFloor): ?>
        <div id="first-floor" class="tab-content">
        <?php else: ?>
        <div id="first-floor" class="tab-content" style="display: none;">
        <?php endif; ?>
            <div class="drawing-container">
                <div class="controls">
                    <button class="control-btn" onclick="zoomIn('first-floor-svg')">Zoom In</button>
                    <button class="control-btn" onclick="zoomOut('first-floor-svg')">Zoom Out</button>
                    <button class="control-btn" onclick="fitToScreen('first-floor-svg')">Fit to Screen</button>
                    <button class="control-btn" onclick="resetView('first-floor-svg')">Reset</button>
                    <br>
                    <button class="control-btn" onclick="panUp('first-floor-svg')">↑</button>
                    <button class="control-btn" onclick="panDown('first-floor-svg')">↓</button>
                    <button class="control-btn" onclick="panLeft('first-floor-svg')">←</button>
                    <button class="control-btn" onclick="panRight('first-floor-svg')">→</button>
                </div>
                <svg id="first-floor-svg" class="drawing-svg" viewBox="0 0 800 600">
                    <?php
                    // Check for first floor data
                    $firstFloorData = [];
                    foreach ($wallCalculations as $wallType => $data) {
                        if (strpos(strtolower($wallType), 'first') !== false ||
                            strpos(strtolower($wallType), '1st') !== false ||
                            strpos(strtolower($wallType), 'ff') !== false) {
                            $firstFloorData[$wallType] = $data;
                        }
                    }

                    if (!empty($firstFloorData)) {
                        echo generateFloorSVG($firstFloorData, 'first');
                    } else {
                        echo generateFloorSVG($wallCalculations, 'first'); // Show ground floor as fallback
                    }
                    ?>
                </svg>
            </div>
            <div class="segment-info">
                <h3>First Floor Segments</h3>
                <?php if (!empty($firstFloorData)): ?>
                    <div class="segment-grid">
                        <?php echo generateSegmentCards($firstFloorData); ?>
                    </div>
                <?php else: ?>
                    <p>First floor data will be available when first floor layers are present in the DXF file. Currently showing ground floor as reference.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Deductions Tab -->
        <div id="deductions" class="tab-content">
            <div class="drawing-container">
                <div class="controls">
                    <button class="control-btn" onclick="zoomIn('deductions-svg')">Zoom In</button>
                    <button class="control-btn" onclick="zoomOut('deductions-svg')">Zoom Out</button>
                    <button class="control-btn" onclick="fitToScreen('deductions-svg')">Fit to Screen</button>
                    <button class="control-btn" onclick="resetView('deductions-svg')">Reset</button>
                    <br>
                    <button class="control-btn" onclick="panUp('deductions-svg')">↑</button>
                    <button class="control-btn" onclick="panDown('deductions-svg')">↓</button>
                    <button class="control-btn" onclick="panLeft('deductions-svg')">←</button>
                    <button class="control-btn" onclick="panRight('deductions-svg')">→</button>
                </div>
                <svg id="deductions-svg" class="drawing-svg" viewBox="0 0 800 600">
                    <?php echo generateDeductionsSVG($wallCalculations, $deductionCalculations); ?>
                </svg>
            </div>
            <div class="segment-info">
                <h3>Deduction Details</h3>
                <div class="segment-grid">
                    <?php echo generateDeductionCards($deductionCalculations); ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentZoom = 1;
        let currentPan = { x: 0, y: 0 };

        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function zoomIn(svgId) {
            currentZoom *= 1.2;
            updateTransform(svgId);
        }

        function zoomOut(svgId) {
            currentZoom /= 1.2;
            updateTransform(svgId);
        }

        function fitToScreen(svgId) {
            const svg = document.getElementById(svgId);
            const container = svg.parentElement;

            // Reset transform first
            currentZoom = 1;
            currentPan = { x: 0, y: 0 };
            updateTransform(svgId);

            // Get the actual content bounds
            try {
                const bbox = svg.getBBox();

                if (bbox.width > 0 && bbox.height > 0) {
                    const containerWidth = container.clientWidth - 40; // Leave some margin
                    const containerHeight = container.clientHeight - 40;

                    const scaleX = containerWidth / bbox.width;
                    const scaleY = containerHeight / bbox.height;
                    currentZoom = Math.min(scaleX, scaleY) * 0.8; // 80% to leave margin

                    // Center the content
                    currentPan.x = (container.clientWidth - bbox.width * currentZoom) / 2 - bbox.x * currentZoom;
                    currentPan.y = (container.clientHeight - bbox.height * currentZoom) / 2 - bbox.y * currentZoom;

                    updateTransform(svgId);
                } else {
                    // If no content, just center the viewBox
                    currentZoom = 0.8;
                    currentPan.x = container.clientWidth * 0.1;
                    currentPan.y = container.clientHeight * 0.1;
                    updateTransform(svgId);
                }
            } catch (e) {
                console.log('Error fitting to screen:', e);
                // Fallback: just set a reasonable zoom and center
                currentZoom = 0.8;
                currentPan.x = 50;
                currentPan.y = 50;
                updateTransform(svgId);
            }
        }

        function resetView(svgId) {
            currentZoom = 1;
            currentPan = { x: 0, y: 0 };
            updateTransform(svgId);
        }

        function updateTransform(svgId) {
            const svg = document.getElementById(svgId);
            const g = svg.querySelector('g');
            if (g) {
                g.setAttribute('transform', `translate(${currentPan.x}, ${currentPan.y}) scale(${currentZoom})`);
            }
        }

        function panUp(svgId) {
            currentPan.y += 50;
            updateTransform(svgId);
        }

        function panDown(svgId) {
            currentPan.y -= 50;
            updateTransform(svgId);
        }

        function panLeft(svgId) {
            currentPan.x += 50;
            updateTransform(svgId);
        }

        function panRight(svgId) {
            currentPan.x -= 50;
            updateTransform(svgId);
        }

        // Initialize fit to screen for all SVGs
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                fitToScreen('ground-floor-svg');
            }, 100);
        });
    </script>
</body>
</html>

<?php
function generateFloorSVG($wallCalculations, $floor = 'ground') {
    $svg = '<g>';
    $colors = [
        'WALLS_10CM' => '#28a745',
        'WALLS_20CM' => '#007cba',
        'WALLS_23CM' => '#6f42c1'
    ];

    // Calculate bounds for proper scaling
    $minX = $minY = PHP_FLOAT_MAX;
    $maxX = $maxY = PHP_FLOAT_MIN;

    // First pass: find bounds
    foreach ($wallCalculations as $wallType => $data) {
        foreach ($data['rectangles'] as $rectangle) {
            $coords = $rectangle['coordinates'];
            foreach ($coords as $coord) {
                $minX = min($minX, $coord[0]);
                $maxX = max($maxX, $coord[0]);
                $minY = min($minY, $coord[1]);
                $maxY = max($maxY, $coord[1]);
            }
        }
    }

    // Add minimal padding to preserve proportions
    $padding = 10;
    $minX -= $padding;
    $minY -= $padding;
    $maxX += $padding;
    $maxY += $padding;

    // Calculate scale to maintain DXF proportions
    $width = $maxX - $minX;
    $height = $maxY - $minY;

    // Determine if we need to rotate (if width > height, rotate 90 degrees)
    $needsRotation = $width > $height;

    if ($needsRotation) {
        // Swap width and height for rotation
        $tempWidth = $width;
        $width = $height;
        $height = $tempWidth;
    }

    $scaleX = 750 / $width;  // Use 750 instead of 800 for margin
    $scaleY = 550 / $height; // Use 550 instead of 600 for margin
    $scale = min($scaleX, $scaleY) * 0.8; // Maintain aspect ratio

    // Center the drawing
    $offsetX = (800 - $width * $scale) / 2;
    $offsetY = (600 - $height * $scale) / 2;

    if ($needsRotation) {
        // Apply rotation and translation
        $centerX = ($minX + $maxX) / 2;
        $centerY = ($minY + $maxY) / 2;
        $svg .= '<g transform="translate(' . $offsetX . ',' . $offsetY . ') scale(' . $scale . ') translate(' . $centerX . ',' . $centerY . ') rotate(90) translate(' . (-$centerX) . ',' . (-$centerY) . ')">';
    } else {
        $svg .= '<g transform="translate(' . $offsetX . ',' . $offsetY . ') scale(' . $scale . ') translate(' . (-$minX) . ',' . (-$minY) . ')">';
    }

    foreach ($wallCalculations as $wallType => $data) {
        $color = $colors[$wallType] ?? '#666';
        $className = strtolower(str_replace('_', '-', $wallType));

        foreach ($data['rectangles'] as $index => $rectangle) {
            $coords = $rectangle['coordinates'];
            if (count($coords) >= 3) {
                $pathData = 'M ' . $coords[0][0] . ' ' . $coords[0][1];
                for ($i = 1; $i < count($coords); $i++) {
                    $pathData .= ' L ' . $coords[$i][0] . ' ' . $coords[$i][1];
                }
                $pathData .= ' Z';

                // Create single line from polygon center line
                $centerX = array_sum(array_column($coords, 0)) / count($coords);
                $centerY = array_sum(array_column($coords, 1)) / count($coords);

                // Find the longest edge for line direction
                $maxLength = 0;
                $lineStart = $coords[0];
                $lineEnd = $coords[1];

                for ($i = 0; $i < count($coords); $i++) {
                    $nextI = ($i + 1) % count($coords);
                    $edgeLength = sqrt(pow($coords[$nextI][0] - $coords[$i][0], 2) + pow($coords[$nextI][1] - $coords[$i][1], 2));
                    if ($edgeLength > $maxLength) {
                        $maxLength = $edgeLength;
                        $lineStart = $coords[$i];
                        $lineEnd = $coords[$nextI];
                    }
                }

                $svg .= '<line x1="' . $lineStart[0] . '" y1="' . $lineStart[1] . '" ';
                $svg .= 'x2="' . $lineEnd[0] . '" y2="' . $lineEnd[1] . '" ';
                $svg .= 'class="wall-segment ' . $className . '" ';
                $svg .= 'stroke="' . $color . '" stroke-width="1" fill="none" ';
                $svg .= 'data-wall-type="' . $wallType . '" data-index="' . $index . '" ';
                $svg .= 'data-length="' . number_format($rectangle['length'], 2) . '" ';
                $svg .= 'data-area="' . number_format($rectangle['area'], 2) . '"/>';
            }
        }
    }

    $svg .= '</g></g>';
    return $svg;
}

function generateSegmentCards($wallCalculations) {
    $html = '';
    
    foreach ($wallCalculations as $wallType => $data) {
        foreach ($data['rectangles'] as $index => $rectangle) {
            $html .= '<div class="segment-card">';
            $html .= '<h4>' . str_replace('_', ' ', $wallType) . ' - Segment ' . ($index + 1) . '</h4>';
            $html .= '<div class="segment-details">';
            $html .= '<strong>Length:</strong> ' . number_format($rectangle['length'], 2) . ' m<br>';
            $html .= '<strong>Area:</strong> ' . number_format($rectangle['area'], 2) . ' m²<br>';
            $html .= '<strong>Width:</strong> ' . number_format($data['width'], 2) . ' m<br>';
            $html .= '<strong>Coordinates:</strong> ' . count($rectangle['coordinates']) . ' points';
            $html .= '</div>';
            $html .= '</div>';
        }
    }
    
    return $html;
}

function generateDeductionsSVG($wallCalculations, $deductionCalculations) {
    // First draw walls as background
    $wallSVG = generateFloorSVG($wallCalculations);

    // Add deduction overlays with different colors for windows
    $deductionColors = [
        'WINDOW_W1' => '#ff6b6b',
        'WINDOW_W2' => '#4ecdc4',
        'WINDOW_W3' => '#45b7d1',
        'WINDOW_W4' => '#96ceb4',
        'WINDOW_KW3' => '#feca57',
        'WINDOW_V1' => '#ff9ff3',
        'DOOR_MD' => '#54a0ff',
        'DOOR_D1' => '#5f27cd',
        'DOOR_D2' => '#00d2d3'
    ];

    $svg = '<g>';
    $svg .= $wallSVG;

    // Add legend for deduction colors
    $legendY = 20;
    $svg .= '<g class="deduction-legend">';
    foreach ($deductionColors as $type => $color) {
        if (isset($deductionCalculations[$type]) && $deductionCalculations[$type]['count'] > 0) {
            $displayName = str_replace(['_', 'WINDOW', 'DOOR'], [' ', 'Win', 'Door'], $type);
            $svg .= '<rect x="10" y="' . $legendY . '" width="15" height="15" fill="' . $color . '" stroke="#000" stroke-width="1"/>';
            $svg .= '<text x="30" y="' . ($legendY + 12) . '" font-size="12" fill="#000">' . $displayName . ' (' . $deductionCalculations[$type]['count'] . ')</text>';
            $legendY += 20;
        }
    }
    $svg .= '</g>';

    $svg .= '</g>';
    return $svg;
}

function generateDeductionCards($deductionCalculations) {
    $html = '';
    
    foreach ($deductionCalculations as $type => $data) {
        if ($data['count'] > 0) {
            $html .= '<div class="segment-card">';
            $html .= '<h4>' . str_replace('_', ' ', $type) . '</h4>';
            $html .= '<div class="segment-details">';
            $html .= '<strong>Count:</strong> ' . $data['count'] . '<br>';
            $html .= '<strong>Default Length:</strong> ' . $data['default_L'] . ' m<br>';
            $html .= '<strong>Default Height:</strong> ' . $data['default_D'] . ' m<br>';
            $html .= '<strong>Total Area:</strong> ' . number_format($data['total_area'], 2) . ' m²<br>';
            $html .= '<strong>Rectangles Found:</strong> ' . count($data['rectangles']);
            $html .= '</div>';
            $html .= '</div>';
        }
    }
    
    return $html;
}
?>
