<?php
require_once 'project_manager.php';

// Get project ID from URL
$projectId = $_GET['project'] ?? 'proj_68750d33be667';

// Get the DXF file path
$projectManager = new ProjectManager();
$project = $projectManager->getProject($projectId);

if (!$project) {
    die('Project not found: ' . $projectId);
}

$dxfFile = $project['dxf_file'];
if (!$dxfFile) {
    die('No DXF file found for project: ' . $projectId);
}

// Process the DXF file with updated Python script
$command = "python3 process_dxf.py \"uploads/$dxfFile\"";
$output = shell_exec($command);

if (!$output) {
    die('Failed to process DXF file');
}

$result = json_decode($output, true);

if (!$result || !$result['success']) {
    die('DXF processing failed: ' . ($result['message'] ?? 'Unknown error'));
}

// Update the project with new data
$updateData = [
    'estimate_data' => $result,
    'status' => 'processed'
];

$updatedProject = $projectManager->updateProject($projectId, $updateData);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Data Updated</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .info { background-color: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0; }
        pre { background-color: #f4f4f4; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .btn { padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }
    </style>
</head>
<body>
    <h1>Project Data Updated Successfully</h1>
    
    <div class="success">
        <h3>✅ Project <?php echo $projectId; ?> has been updated with floor-grouped data</h3>
        <p><strong>DXF File:</strong> <?php echo $dxfFile; ?></p>
        <p><strong>Processed At:</strong> <?php echo $result['data']['processed_at']; ?></p>
    </div>

    <div class="info">
        <h3>Floor Boundaries Detected:</h3>
        <?php 
        $floorBoundaries = $result['data']['info']['wall_calculations']['floor_boundaries'] ?? [];
        foreach ($floorBoundaries as $floor => $boundary) {
            if ($boundary) {
                echo "<p><strong>$floor:</strong> " . count($boundary['coordinates']) . " coordinate points</p>";
            }
        }
        ?>
    </div>

    <div class="info">
        <h3>Layer Areas by Floor:</h3>
        <?php 
        $layerAreas = $result['data']['info']['layer_areas'] ?? [];
        foreach ($layerAreas as $floor => $areas) {
            echo "<p><strong>$floor:</strong> " . count($areas) . " layer(s)</p>";
            if (!empty($areas)) {
                foreach ($areas as $layer => $data) {
                    if (is_array($data) && isset($data['total_area'])) {
                        echo "<p style='margin-left: 20px;'>- $layer: " . number_format($data['total_area'], 2) . " sq units</p>";
                    } elseif (is_numeric($data)) {
                        echo "<p style='margin-left: 20px;'>- $layer: " . number_format($data, 2) . " sq units</p>";
                    }
                }
            }
        }
        ?>
    </div>

    <div class="info">
        <h3>MAIN_SLAB Analysis:</h3>
        <?php
        // Check if MAIN_SLAB_TOTAL exists in layer_entities
        $layerEntities = $result['data']['info']['layer_entities'] ?? [];
        if (isset($layerEntities['MAIN_SLAB_TOTAL'])) {
            echo "<p>✅ MAIN_SLAB_TOTAL found in DXF with " . ($layerEntities['MAIN_SLAB_TOTAL']['LWPOLYLINE'] ?? 0) . " entities</p>";
        } else {
            echo "<p>❌ MAIN_SLAB_TOTAL not found in DXF</p>";
        }

        // Check if it appears in any floor's layer_areas
        $foundInFloor = false;
        foreach ($layerAreas as $floor => $areas) {
            if (isset($areas['MAIN_SLAB_TOTAL'])) {
                echo "<p>✅ MAIN_SLAB_TOTAL found in $floor</p>";
                $foundInFloor = true;
            }
        }
        
        if (!$foundInFloor && isset($layerEntities['MAIN_SLAB_TOTAL'])) {
            echo "<p>⚠️ MAIN_SLAB_TOTAL exists in DXF but not assigned to any floor (coordinates may be outside floor boundaries)</p>";
        }
        ?>
    </div>

    <div style="margin-top: 30px;">
        <a href="cpwd_estimate.php?project=<?php echo $projectId; ?>" class="btn">View Updated Estimate</a>
        <a href="test_specific_project.php?project=<?php echo $projectId; ?>" class="btn">Test Floor Data</a>
        <a href="index.php" class="btn" style="background-color: #6c757d;">Back to Projects</a>
    </div>

    <div style="margin-top: 30px;">
        <h3>Debug: Raw Processing Result</h3>
        <pre><?php echo json_encode($result, JSON_PRETTY_PRINT); ?></pre>
    </div>
</body>
</html>
