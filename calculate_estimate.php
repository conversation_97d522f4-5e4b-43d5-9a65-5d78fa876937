<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $projectId = $input['project_id'] ?? null;
    $inputData = $input['input_data'] ?? null;
    
    if (!$projectId || !$inputData) {
        throw new Exception('Missing required fields: project_id and input_data');
    }
    
    require_once 'project_manager.php';
    $projectManager = new ProjectManager();
    
    // Get current project
    $project = $projectManager->getProject($projectId);
    if (!$project) {
        throw new Exception('Project not found');
    }
    
    // Get wall calculations from project data
    $wallCalculations = [];
    if (isset($project['estimate_data']['data']['info']['wall_calculations'])) {
        $wallCalculations = $project['estimate_data']['data']['info']['wall_calculations'];
    }
    
    // Calculate Item 1 totals
    $item1TotalQty = 0;
    $item1Rate = floatval($inputData['item1']['rate'] ?? 145.20);
    $item1Calculations = [];
    
    foreach ($wallCalculations as $wallType => $wallData) {
        if (isset($wallData['rectangles'])) {
            foreach ($wallData['rectangles'] as $index => $rectangle) {
                $length = floatval($rectangle['length'] ?? 0);
                $b = floatval($inputData['item1']['defaultB'] ?? 0.7);
                $d = floatval($inputData['item1']['defaultD'] ?? 1.2);
                
                // Check if there's specific input for this wall
                $wallKey = $wallType . '_' . $index;
                if (isset($inputData['item1']['walls'][$wallKey])) {
                    $b = floatval($inputData['item1']['walls'][$wallKey]['b'] ?? $b);
                    $d = floatval($inputData['item1']['walls'][$wallKey]['d'] ?? $d);
                }
                
                $qty = 1 * $length * $b * $d;
                $item1TotalQty += $qty;
                
                $item1Calculations[] = [
                    'wallType' => $wallType,
                    'index' => $index,
                    'length' => $length,
                    'b' => $b,
                    'd' => $d,
                    'qty' => round($qty, 2)
                ];
            }
        }
    }
    
    $item1TotalAmount = $item1TotalQty * $item1Rate;
    
    // Calculate Item 2 totals
    $item2BelowGLQty = 0;
    $item2BasementQty = 0;
    $item2Rate = floatval($inputData['item2']['rate'] ?? 4190.00);
    $item2Calculations = [];
    
    foreach ($wallCalculations as $wallType => $wallData) {
        if (isset($wallData['rectangles'])) {
            foreach ($wallData['rectangles'] as $index => $rectangle) {
                $length = floatval($rectangle['length'] ?? 0);
                
                // Below GL calculation
                $belowGLB = floatval($inputData['item2']['belowGL']['defaultB'] ?? 0.53);
                $belowGLD = floatval($inputData['item2']['belowGL']['defaultD'] ?? 1.2);
                $belowGLQty = 1 * $length * $belowGLB * $belowGLD;
                $item2BelowGLQty += $belowGLQty;
                
                // Basement calculation
                $basementB = floatval($inputData['item2']['basement']['defaultB'] ?? 0.3);
                $basementD = floatval($inputData['item2']['basement']['defaultD'] ?? 0.6);
                $basementQty = 1 * $length * $basementB * $basementD;
                $item2BasementQty += $basementQty;
                
                $item2Calculations[] = [
                    'wallType' => $wallType,
                    'index' => $index,
                    'length' => $length,
                    'belowGL' => [
                        'b' => $belowGLB,
                        'd' => $belowGLD,
                        'qty' => round($belowGLQty, 2)
                    ],
                    'basement' => [
                        'b' => $basementB,
                        'd' => $basementD,
                        'qty' => round($basementQty, 2)
                    ]
                ];
            }
        }
    }
    
    $item2TotalQty = $item2BelowGLQty + $item2BasementQty;
    $item2TotalAmount = $item2TotalQty * $item2Rate;
    
    // Prepare response
    $response = [
        'success' => true,
        'calculations' => [
            'item1' => [
                'totalQty' => round($item1TotalQty, 2),
                'rate' => $item1Rate,
                'totalAmount' => round($item1TotalAmount, 2),
                'details' => $item1Calculations
            ],
            'item2' => [
                'belowGLQty' => round($item2BelowGLQty, 2),
                'basementQty' => round($item2BasementQty, 2),
                'totalQty' => round($item2TotalQty, 2),
                'rate' => $item2Rate,
                'totalAmount' => round($item2TotalAmount, 2),
                'details' => $item2Calculations
            ]
        ]
    ];
    
    // Save the input data
    if (!isset($project['estimate_data'])) {
        $project['estimate_data'] = [];
    }
    $project['estimate_data']['user_inputs'] = $inputData;
    $project['estimate_data']['calculations'] = $response['calculations'];
    $project['updated_at'] = date('Y-m-d H:i:s');
    
    $projectManager->updateProject($projectId, $project);
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
