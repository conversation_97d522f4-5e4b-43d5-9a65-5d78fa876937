#!/usr/bin/env python3
"""
DXF File Processing Script for SmartEstimate
This script processes uploaded DXF files and extracts relevant information.
"""

import sys
import json
import os
import traceback
from datetime import datetime

def log_error(message):
    """Log error messages to a file"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_message = f"[{timestamp}] ERROR: {message}\n"
    
    try:
        with open('python_error.log', 'a') as f:
            f.write(log_message)
    except:
        pass  # Fail silently if logging fails

def validate_dxf_file(file_path):
    """Basic validation of DXF file format"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            first_line = f.readline().strip()
            if first_line != '0':
                return False, "Invalid DXF format: File should start with '0'"
            
            # Read a few more lines to check for SECTION
            for _ in range(10):
                line = f.readline().strip()
                if line == 'SECTION':
                    return True, "Valid DXF format detected"
            
            return False, "Invalid DXF format: SECTION not found in header"
    
    except UnicodeDecodeError:
        # Try with different encoding
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                first_line = f.readline().strip()
                return first_line == '0', "DXF format validation with latin-1 encoding"
        except:
            return False, "Unable to read file with any encoding"
    
    except Exception as e:
        return False, f"Error reading file: {str(e)}"

def extract_dxf_info(file_path):
    """Extract basic information from DXF file"""
    info = {
        'entities': {},
        'layers': set(),
        'blocks': set(),
        'file_size': 0,
        'line_count': 0,
        'layer_entities': {},  # Store entities by layer
        'wall_calculations': {}  # Store wall layer calculations
    }

    try:
        # Get file size
        info['file_size'] = os.path.getsize(file_path)

        current_section = None
        current_entity = None
        current_layer = None
        entity_count = 0

        # Store entities with their layer information
        entities_data = []

        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            info['line_count'] = len(lines)

            i = 0
            while i < len(lines):
                line = lines[i].strip()

                # Track sections
                if line == 'SECTION':
                    if i + 1 < len(lines):
                        section_name = lines[i + 2].strip()
                        current_section = section_name

                # Track entities in ENTITIES section
                elif current_section == 'ENTITIES' and line == '0' and i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if next_line not in ['ENDSEC', 'EOF']:
                        current_entity = next_line
                        current_layer = None

                        # Count entities
                        if current_entity in info['entities']:
                            info['entities'][current_entity] += 1
                        else:
                            info['entities'][current_entity] = 1
                        entity_count += 1

                        # Extract entity data for specific types
                        if current_entity in ['LWPOLYLINE', 'POLYLINE', 'LINE', 'CIRCLE', 'ARC']:
                            entity_data = {
                                'type': current_entity,
                                'layer': None,
                                'coordinates': [],
                                'area': 0,
                                'length': 0
                            }
                            entities_data.append(entity_data)

                # Track layers
                elif line == '8' and i + 1 < len(lines):  # Layer code
                    layer_name = lines[i + 1].strip()
                    if layer_name and layer_name != '0':
                        info['layers'].add(layer_name)
                        current_layer = layer_name

                        # Update current entity's layer
                        if entities_data and entities_data[-1]['layer'] is None:
                            entities_data[-1]['layer'] = layer_name

                        # Initialize layer entity count
                        if layer_name not in info['layer_entities']:
                            info['layer_entities'][layer_name] = {}
                        if current_entity and current_entity not in info['layer_entities'][layer_name]:
                            info['layer_entities'][layer_name][current_entity] = 0
                        if current_entity:
                            info['layer_entities'][layer_name][current_entity] += 1

                # Track blocks
                elif current_section == 'BLOCKS' and line == '2' and i + 1 < len(lines):
                    block_name = lines[i + 1].strip()
                    if block_name and not block_name.startswith('*'):
                        info['blocks'].add(block_name)

                i += 1

        # Convert sets to lists for JSON serialization
        info['layers'] = list(info['layers'])
        info['blocks'] = list(info['blocks'])
        info['total_entities'] = entity_count

        # Calculate wall layer information
        info['wall_calculations'] = calculate_wall_layers(file_path, info['layers'])

        # Calculate deduction information
        info['deduction_calculations'] = calculate_deduction_layers(file_path, info['layers'], info['layer_entities'])

        # Calculate RCC layer information
        info['rcc_calculations'] = calculate_rcc_layers(file_path, info['layers'], info['layer_entities'])

        # Calculate layer areas for slabs
        info['layer_areas'] = calculate_layer_areas(file_path, info['layers'])

        # Calculate flooring layer rectangles
        info['flooring_calculations'] = calculate_flooring_layers(file_path, info['layers'])

        return info

    except Exception as e:
        log_error(f"Error extracting DXF info: {str(e)}")
        return None

def process_dxf_file(file_path):
    """Main function to process DXF file"""
    result = {
        'success': False,
        'message': '',
        'data': {}
    }
    
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            result['message'] = f"File not found: {file_path}"
            return result
        
        # Validate DXF format
        is_valid, validation_message = validate_dxf_file(file_path)
        if not is_valid:
            result['message'] = f"Invalid DXF file: {validation_message}"
            return result
        
        # Extract information from DXF
        dxf_info = extract_dxf_info(file_path)
        if dxf_info is None:
            result['message'] = "Failed to extract information from DXF file"
            return result
        
        # Prepare result
        result['success'] = True
        result['message'] = "DXF file processed successfully"
        result['data'] = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'processed_at': datetime.now().isoformat(),
            'validation': validation_message,
            'info': dxf_info
        }
        
        # Add some basic analysis
        analysis = analyze_dxf_content(dxf_info)
        result['data']['analysis'] = analysis
        
        return result
    
    except Exception as e:
        log_error(f"Error processing DXF file {file_path}: {str(e)}\n{traceback.format_exc()}")
        result['message'] = f"Processing error: {str(e)}"
        return result

def calculate_wall_layers(file_path, layers):
    """Calculate areas and lengths for wall layers"""
    wall_calculations = {
        'WALLS_10CM': {'total_area': 0, 'total_length': 0, 'width': 0.10, 'rectangles': []},
        'WALLS_20CM': {'total_area': 0, 'total_length': 0, 'width': 0.20, 'rectangles': []},
        'WALLS_23CM': {'total_area': 0, 'total_length': 0, 'width': 0.23, 'rectangles': []}
    }

    ground_floor_boundary = None

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

            current_section = None
            current_entity = None
            current_layer = None
            entity_data = {}

            i = 0
            while i < len(lines):
                line = lines[i].strip()

                # Track sections
                if line == 'SECTION':
                    if i + 2 < len(lines):
                        section_name = lines[i + 2].strip()
                        current_section = section_name

                # Process entities in ENTITIES section
                elif current_section == 'ENTITIES':
                    if line == '0' and i + 1 < len(lines):
                        # Save previous entity if it was a wall layer
                        if current_entity and current_layer in wall_calculations and entity_data:
                            process_wall_entity(entity_data, wall_calculations[current_layer])
                        elif current_entity and current_layer == 'GROUND_FLOOR' and entity_data:
                            ground_floor_boundary = entity_data

                        # Start new entity
                        next_line = lines[i + 1].strip()
                        if next_line not in ['ENDSEC', 'EOF']:
                            current_entity = next_line
                            current_layer = None
                            entity_data = {'type': current_entity, 'coordinates': []}

                    elif line == '8' and i + 1 < len(lines):  # Layer code
                        current_layer = lines[i + 1].strip()
                        entity_data['layer'] = current_layer

                    elif line in ['10', '20'] and i + 1 < len(lines):  # X, Y coordinates
                        try:
                            coord_value = float(lines[i + 1].strip())
                            if line == '10':  # X coordinate
                                entity_data['coordinates'].append([coord_value, 0])
                            elif line == '20' and entity_data['coordinates']:  # Y coordinate
                                entity_data['coordinates'][-1][1] = coord_value
                        except ValueError:
                            pass

                    elif line in ['11', '21'] and i + 1 < len(lines):  # Second point for lines
                        try:
                            coord_value = float(lines[i + 1].strip())
                            if line == '11':  # X coordinate of second point
                                if len(entity_data['coordinates']) == 1:
                                    entity_data['coordinates'].append([coord_value, 0])
                            elif line == '21' and len(entity_data['coordinates']) >= 2:  # Y coordinate
                                entity_data['coordinates'][-1][1] = coord_value
                        except ValueError:
                            pass

                i += 1

            # Process last entity
            if current_entity and current_layer in wall_calculations and entity_data:
                process_wall_entity(entity_data, wall_calculations[current_layer])
            elif current_entity and current_layer == 'GROUND_FLOOR' and entity_data:
                ground_floor_boundary = entity_data

        # Calculate totals and lengths
        for layer_name, calc_data in wall_calculations.items():
            if calc_data['total_area'] > 0:
                calc_data['total_length'] = calc_data['total_area'] / calc_data['width']

        return wall_calculations

    except Exception as e:
        log_error(f"Error calculating wall layers: {str(e)}")
        return wall_calculations

def process_wall_entity(entity_data, layer_calc):
    """Process a single wall entity and calculate its area"""
    try:
        if entity_data['type'] in ['LWPOLYLINE', 'POLYLINE'] and len(entity_data['coordinates']) >= 3:
            # Calculate area of polygon using shoelace formula
            coords = entity_data['coordinates']
            area = 0
            n = len(coords)

            for i in range(n):
                j = (i + 1) % n
                area += coords[i][0] * coords[j][1]
                area -= coords[j][0] * coords[i][1]

            area = abs(area) / 2.0

            if area > 0:
                layer_calc['total_area'] += area
                layer_calc['rectangles'].append({
                    'area': area,
                    'coordinates': coords,
                    'length': area / layer_calc['width']
                })

        elif entity_data['type'] == 'LINE' and len(entity_data['coordinates']) == 2:
            # For lines, calculate length and assume width
            x1, y1 = entity_data['coordinates'][0]
            x2, y2 = entity_data['coordinates'][1]
            length = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
            area = length * layer_calc['width']

            if area > 0:
                layer_calc['total_area'] += area
                layer_calc['rectangles'].append({
                    'area': area,
                    'coordinates': entity_data['coordinates'],
                    'length': length
                })

    except Exception as e:
        log_error(f"Error processing wall entity: {str(e)}")

def calculate_deduction_layers(file_path, layers, layer_entities):
    """Calculate deduction data for doors, windows, and openings"""
    deduction_calculations = {}

    # Define deduction types with their default dimensions
    deduction_types = {
        'DOOR_D': {'default_L': 1.0, 'default_D': 2.1},
        'DOOR_D1': {'default_L': 1.0, 'default_D': 2.1},
        'DOOR_D2': {'default_L': 1.0, 'default_D': 2.1},
        'DOOR_MD': {'default_L': 1.2, 'default_D': 2.1},
        'OPENINGS': {'default_L': 1.0, 'default_D': 2.1},
        'WINDOW_KW3': {'default_L': 1.5, 'default_D': 1.1},
        'WINDOW_V1': {'default_L': 0.6, 'default_D': 0.6},
        'WINDOW_W1': {'default_L': 0.5, 'default_D': 1.5},
        'WINDOW_W2': {'default_L': 1.0, 'default_D': 1.5},
        'WINDOW_W3': {'default_L': 1.5, 'default_D': 1.5},
        'WINDOW_W4': {'default_L': 2.0, 'default_D': 1.5}
    }

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            lines = file.readlines()

        current_section = None
        current_entity = None
        current_layer = None
        entity_data = None
        i = 0

        while i < len(lines):
            line = lines[i].strip()

            # Track sections
            if line == 'SECTION' and i + 1 < len(lines):
                current_section = lines[i + 1].strip()
            elif line == 'ENDSEC':
                current_section = None

            # Process entities in ENTITIES section
            elif current_section == 'ENTITIES':
                if line == '0' and i + 1 < len(lines):
                    # Process previous entity if it was a deduction layer
                    if current_entity and current_layer in deduction_types and entity_data:
                        process_deduction_entity(entity_data, deduction_calculations, current_layer, deduction_types[current_layer])

                    # Start new entity
                    next_line = lines[i + 1].strip()
                    if next_line not in ['ENDSEC', 'EOF']:
                        current_entity = next_line
                        current_layer = None
                        entity_data = {'type': current_entity, 'coordinates': []}

                elif line == '8' and i + 1 < len(lines):  # Layer code
                    current_layer = lines[i + 1].strip()
                    if entity_data:
                        entity_data['layer'] = current_layer

                elif line == '10' and i + 1 < len(lines):  # X coordinate
                    try:
                        x = float(lines[i + 1].strip())
                        if entity_data:
                            if 'temp_point' not in entity_data:
                                entity_data['temp_point'] = [x, 0]
                            else:
                                entity_data['temp_point'][0] = x
                    except ValueError:
                        pass

                elif line == '20' and i + 1 < len(lines):  # Y coordinate
                    try:
                        y = float(lines[i + 1].strip())
                        if entity_data and 'temp_point' in entity_data:
                            entity_data['temp_point'][1] = y
                            entity_data['coordinates'].append(entity_data['temp_point'][:])
                            entity_data['temp_point'] = [0, 0]  # Reset for next point
                    except ValueError:
                        pass

            i += 1

        # Process last entity
        if current_entity and current_layer in deduction_types and entity_data:
            process_deduction_entity(entity_data, deduction_calculations, current_layer, deduction_types[current_layer])

        # Add count information from layer_entities and ensure all types are represented
        for layer_name in deduction_types.keys():
            if layer_name in layer_entities and 'LWPOLYLINE' in layer_entities[layer_name]:
                count = layer_entities[layer_name]['LWPOLYLINE']
                if layer_name in deduction_calculations:
                    deduction_calculations[layer_name]['count'] = count
                else:
                    deduction_calculations[layer_name] = {
                        'count': count,
                        'rectangles': [],
                        'total_area': 0,
                        'default_L': deduction_types[layer_name]['default_L'],
                        'default_D': deduction_types[layer_name]['default_D']
                    }
            else:
                # Ensure all deduction types are present even if count is 0
                if layer_name not in deduction_calculations:
                    deduction_calculations[layer_name] = {
                        'count': 0,
                        'rectangles': [],
                        'total_area': 0,
                        'default_L': deduction_types[layer_name]['default_L'],
                        'default_D': deduction_types[layer_name]['default_D']
                    }

    except Exception as e:
        log_error(f"Error calculating deduction layers: {str(e)}")

    return deduction_calculations

def process_deduction_entity(entity_data, deduction_calculations, layer_name, defaults):
    """Process a single deduction entity and calculate its dimensions"""
    try:
        if layer_name not in deduction_calculations:
            deduction_calculations[layer_name] = {
                'count': 0,
                'rectangles': [],
                'total_area': 0,
                'default_L': defaults['default_L'],
                'default_D': defaults['default_D']
            }

        if entity_data['type'] in ['LWPOLYLINE', 'POLYLINE'] and len(entity_data['coordinates']) >= 3:
            # Calculate area and dimensions of polygon
            coords = entity_data['coordinates']
            area = 0
            n = len(coords)

            for i in range(n):
                j = (i + 1) % n
                area += coords[i][0] * coords[j][1]
                area -= coords[j][0] * coords[i][1]

            area = abs(area) / 2.0

            # Calculate length and width from bounding box
            if len(coords) >= 3:  # Any polygon
                x_coords = [coord[0] for coord in coords]
                y_coords = [coord[1] for coord in coords]
                bbox_length = max(x_coords) - min(x_coords)
                bbox_width = max(y_coords) - min(y_coords)

                # For rectangles, calculate actual edge lengths
                if len(coords) >= 4:
                    # Calculate distances between consecutive points
                    edge_lengths = []
                    for i in range(len(coords)):
                        j = (i + 1) % len(coords)
                        dx = coords[j][0] - coords[i][0]
                        dy = coords[j][1] - coords[i][1]
                        edge_length = (dx*dx + dy*dy)**0.5
                        if edge_length > 0.01:  # Ignore very small edges
                            edge_lengths.append(edge_length)

                    if len(edge_lengths) >= 2:
                        # For rectangles, we should have pairs of equal lengths
                        edge_lengths.sort()
                        if len(edge_lengths) >= 4:
                            # Take the two different lengths (width and height)
                            length = max(edge_lengths[-1], edge_lengths[-2])
                            width = min(edge_lengths[0], edge_lengths[1])
                        else:
                            length = max(edge_lengths)
                            width = min(edge_lengths)
                    else:
                        # Fallback to bounding box
                        length = max(bbox_length, bbox_width)
                        width = min(bbox_length, bbox_width)
                else:
                    # Fallback to bounding box
                    length = max(bbox_length, bbox_width)
                    width = min(bbox_length, bbox_width)

                # Ensure length is the larger dimension
                if width > length:
                    length, width = width, length

                # Calculate wall thickness based on layer type and actual dimensions
                wall_thickness = calculate_wall_thickness(layer_name, width, length, area)

                deduction_calculations[layer_name]['rectangles'].append({
                    'area': area,
                    'length': length,
                    'width': width,
                    'wall_thickness': wall_thickness,
                    'bbox_length': bbox_length,
                    'bbox_width': bbox_width,
                    'coordinates': coords
                })
                deduction_calculations[layer_name]['total_area'] += area

    except Exception as e:
        log_error(f"Error processing deduction entity: {str(e)}")

def calculate_wall_thickness(layer_name, width, length, area):
    """Calculate wall thickness based on layer type and dimensions"""
    try:
        # Default thickness based on layer type
        if 'DOOR' in layer_name:
            # Doors are typically in thicker walls
            if 'D2' in layer_name:
                return 0.1  # D2 doors typically in 10CM walls
            else:
                return 0.2  # Other doors typically in 20CM walls
        elif 'WINDOW' in layer_name:
            if 'V1' in layer_name or 'KW3' in layer_name:
                return 0.1  # V1 and KW3 windows typically in 10CM walls
            else:
                return 0.2  # Other windows typically in 20CM walls
        elif 'OPENING' in layer_name:
            # For openings, try to determine from area/length ratio
            if length > 0:
                calculated_thickness = area / length
                # Round to nearest standard wall thickness
                if calculated_thickness < 0.15:
                    return 0.1
                elif calculated_thickness < 0.215:
                    return 0.2
                else:
                    return 0.23
            return 0.2  # Default for openings
        else:
            return 0.2  # Default thickness

    except Exception as e:
        log_error(f"Error calculating wall thickness: {str(e)}")
        return 0.2  # Default fallback

def analyze_dxf_content(dxf_info):
    """Perform basic analysis of DXF content"""
    analysis = {
        'complexity': 'Unknown',
        'drawing_type': 'Unknown',
        'recommendations': []
    }
    
    try:
        total_entities = dxf_info.get('total_entities', 0)
        entity_types = len(dxf_info.get('entities', {}))
        layer_count = len(dxf_info.get('layers', []))
        
        # Determine complexity
        if total_entities < 100:
            analysis['complexity'] = 'Simple'
        elif total_entities < 1000:
            analysis['complexity'] = 'Medium'
        else:
            analysis['complexity'] = 'Complex'
        
        # Analyze entity types
        entities = dxf_info.get('entities', {})
        if 'LINE' in entities or 'POLYLINE' in entities:
            if 'CIRCLE' in entities or 'ARC' in entities:
                analysis['drawing_type'] = 'Mixed (Lines and Curves)'
            else:
                analysis['drawing_type'] = 'Linear Drawing'
        elif 'CIRCLE' in entities or 'ARC' in entities:
            analysis['drawing_type'] = 'Curved Drawing'
        elif 'TEXT' in entities or 'MTEXT' in entities:
            analysis['drawing_type'] = 'Text-heavy Drawing'
        
        # Generate recommendations
        if layer_count > 10:
            analysis['recommendations'].append('Consider organizing layers for better management')
        
        if total_entities > 5000:
            analysis['recommendations'].append('Large drawing - processing may take longer')
        
        if entity_types < 3:
            analysis['recommendations'].append('Simple drawing with few entity types')
        
        # Add entity summary
        analysis['entity_summary'] = {
            'total_entities': total_entities,
            'entity_types': entity_types,
            'layer_count': layer_count,
            'most_common_entities': sorted(entities.items(), key=lambda x: x[1], reverse=True)[:5]
        }
        
    except Exception as e:
        log_error(f"Error in DXF analysis: {str(e)}")
        analysis['error'] = str(e)
    
    return analysis

def calculate_rcc_layers(file_path, layers, layer_entities):
    """Calculate RCC column and beam data"""
    rcc_calculations = {
        'columns': {},
        'beams': {}
    }

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            lines = file.readlines()

        current_section = None
        current_entity = None
        current_layer = None
        entity_data = None

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # Track sections
            if line == 'SECTION' and i + 1 < len(lines):
                current_section = lines[i + 1].strip()
            elif line == 'ENDSEC':
                current_section = None

            # Process entities in ENTITIES section
            elif current_section == 'ENTITIES':
                if line == '0' and i + 1 < len(lines):
                    # Process previous entity if it was an RCC layer
                    if current_entity and current_layer in ['RCC_COLUMN', 'RCC_BEAM'] and entity_data:
                        process_rcc_entity(entity_data, rcc_calculations, current_layer)

                    # Start new entity
                    next_line = lines[i + 1].strip()
                    if next_line not in ['ENDSEC', 'EOF']:
                        current_entity = next_line
                        current_layer = None
                        entity_data = {'type': current_entity, 'coordinates': []}

                elif line == '8' and i + 1 < len(lines):  # Layer code
                    current_layer = lines[i + 1].strip()
                    if entity_data:
                        entity_data['layer'] = current_layer

                elif line in ['10', '20'] and i + 1 < len(lines):  # X, Y coordinates
                    try:
                        coord_value = float(lines[i + 1].strip())
                        if entity_data:
                            if line == '10':  # X coordinate
                                entity_data.setdefault('temp_coords', []).append([coord_value, 0])
                            elif line == '20' and 'temp_coords' in entity_data:  # Y coordinate
                                if entity_data['temp_coords']:
                                    entity_data['temp_coords'][-1][1] = coord_value
                                    entity_data['coordinates'].append(entity_data['temp_coords'][-1])
                    except ValueError:
                        pass

            i += 1

        # Process final entity
        if current_entity and current_layer in ['RCC_COLUMN', 'RCC_BEAM'] and entity_data:
            process_rcc_entity(entity_data, rcc_calculations, current_layer)

    except Exception as e:
        log_error(f"Error calculating RCC layers: {str(e)}")

    return rcc_calculations

def process_rcc_entity(entity_data, rcc_calculations, layer_name):
    """Process RCC column or beam entity"""
    try:
        if entity_data['type'] in ['LWPOLYLINE', 'POLYLINE'] and len(entity_data['coordinates']) >= 3:
            coords = entity_data['coordinates']

            # Calculate bounding box dimensions
            x_coords = [coord[0] for coord in coords]
            y_coords = [coord[1] for coord in coords]

            length = max(x_coords) - min(x_coords)
            breadth = max(y_coords) - min(y_coords)

            # Calculate actual area using shoelace formula
            area = 0
            n = len(coords)
            for i in range(n):
                j = (i + 1) % n
                area += coords[i][0] * coords[j][1]
                area -= coords[j][0] * coords[i][1]
            area = abs(area) / 2.0

            if layer_name == 'RCC_COLUMN':
                column_id = f"COLUMN_{len(rcc_calculations['columns']) + 1}"
                rcc_calculations['columns'][column_id] = {
                    'length': round(length, 2),
                    'breadth': round(breadth, 2),
                    'area': round(area, 2),
                    'coordinates': coords
                }
                log_error(f"Added column {column_id}: {length:.2f} x {breadth:.2f}")
            elif layer_name == 'RCC_BEAM':
                beam_id = f"BEAM_{len(rcc_calculations['beams']) + 1}"
                # For beams, length is the longer dimension
                beam_length = max(length, breadth)
                beam_width = min(length, breadth)
                rcc_calculations['beams'][beam_id] = {
                    'length': round(beam_length, 2),
                    'width': round(beam_width, 2),
                    'area': round(area, 2),
                    'coordinates': coords
                }
                log_error(f"Added beam {beam_id}: {beam_length:.2f} x {beam_width:.2f}")

    except Exception as e:
        log_error(f"Error processing RCC entity: {str(e)}")

def calculate_layer_areas(file_path, layers):
    """Calculate areas for specific layers like MAIN_SLAB and SUNSHADE"""
    layer_areas = {}
    target_layers = ['MAIN_SLAB_TOTAL', 'MAIN_SLAB_DEDUCTION', 'SUNSHADE']

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            lines = file.readlines()

        current_section = None
        current_entity = None
        current_layer = None
        entity_data = None

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # Track sections
            if line == 'SECTION' and i + 1 < len(lines):
                current_section = lines[i + 1].strip()
            elif line == 'ENDSEC':
                current_section = None

            # Process entities in ENTITIES section
            elif current_section == 'ENTITIES':
                if line == '0' and i + 1 < len(lines):
                    # Process previous entity if it was a target layer
                    if current_entity and current_layer in target_layers and entity_data:
                        process_area_entity(entity_data, layer_areas, current_layer)

                    # Start new entity
                    next_line = lines[i + 1].strip()
                    if next_line not in ['ENDSEC', 'EOF']:
                        current_entity = next_line
                        current_layer = None
                        entity_data = {'type': current_entity, 'coordinates': []}

                elif line == '8' and i + 1 < len(lines):  # Layer code
                    current_layer = lines[i + 1].strip()
                    if entity_data:
                        entity_data['layer'] = current_layer

                elif line in ['10', '20'] and i + 1 < len(lines):  # X, Y coordinates
                    try:
                        coord_value = float(lines[i + 1].strip())
                        if entity_data:
                            if line == '10':  # X coordinate
                                entity_data.setdefault('temp_coords', []).append([coord_value, 0])
                            elif line == '20' and 'temp_coords' in entity_data:  # Y coordinate
                                if entity_data['temp_coords']:
                                    entity_data['temp_coords'][-1][1] = coord_value
                                    entity_data['coordinates'].append(entity_data['temp_coords'][-1])
                    except ValueError:
                        pass

            i += 1

        # Process final entity
        if current_entity and current_layer in target_layers and entity_data:
            process_area_entity(entity_data, layer_areas, current_layer)

    except Exception as e:
        log_error(f"Error calculating layer areas: {str(e)}")

    return layer_areas

def process_area_entity(entity_data, layer_areas, layer_name):
    """Process area entity for layers like MAIN_SLAB and SUNSHADE"""
    try:
        if entity_data['type'] in ['LWPOLYLINE', 'POLYLINE'] and len(entity_data['coordinates']) >= 3:
            coords = entity_data['coordinates']

            # Calculate area using shoelace formula
            area = 0
            n = len(coords)
            for i in range(n):
                j = (i + 1) % n
                area += coords[i][0] * coords[j][1]
                area -= coords[j][0] * coords[i][1]
            area = abs(area) / 2.0

            # For SUNSHADE, also calculate dimensions
            if layer_name == 'SUNSHADE':
                x_coords = [coord[0] for coord in coords]
                y_coords = [coord[1] for coord in coords]
                length = max(x_coords) - min(x_coords)
                breadth = max(y_coords) - min(y_coords)

                if layer_name not in layer_areas:
                    layer_areas[layer_name] = {'entities': [], 'total_area': 0}

                layer_areas[layer_name]['entities'].append({
                    'area': round(area, 2),
                    'length': round(length, 2),
                    'breadth': round(breadth, 2),
                    'coordinates': coords
                })
                layer_areas[layer_name]['total_area'] += area
            else:
                # For other layers, just sum the area
                if layer_name not in layer_areas:
                    layer_areas[layer_name] = 0
                layer_areas[layer_name] += area

    except Exception as e:
        log_error(f"Error processing area entity: {str(e)}")

def calculate_flooring_layers(file_path, layers):
    """Calculate individual rectangles for flooring layers"""
    flooring_calculations = {
        'FLOORING_KITCHEN': {'rectangles': [], 'total_area': 0},
        'FLOORING_MAIN': {'rectangles': [], 'total_area': 0},
        'FLOORING_TOILET': {'rectangles': [], 'total_area': 0}
    }

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            lines = file.readlines()

        current_section = None
        current_entity = None
        current_layer = None
        entity_data = None

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # Track sections
            if line == 'SECTION' and i + 1 < len(lines):
                current_section = lines[i + 1].strip()
            elif line == 'ENDSEC':
                current_section = None

            # Process entities in ENTITIES section
            elif current_section == 'ENTITIES':
                if line == '0' and i + 1 < len(lines):
                    # Process previous entity if it was a flooring layer
                    if current_entity and current_layer in flooring_calculations and entity_data:
                        process_flooring_entity(entity_data, flooring_calculations, current_layer)

                    # Start new entity
                    next_line = lines[i + 1].strip()
                    if next_line not in ['ENDSEC', 'EOF']:
                        current_entity = next_line
                        current_layer = None
                        entity_data = {'type': current_entity, 'coordinates': []}

                elif line == '8' and i + 1 < len(lines):  # Layer code
                    current_layer = lines[i + 1].strip()
                    if entity_data:
                        entity_data['layer'] = current_layer

                elif line in ['10', '20'] and i + 1 < len(lines):  # X, Y coordinates
                    try:
                        coord_value = float(lines[i + 1].strip())
                        if entity_data:
                            if line == '10':  # X coordinate
                                entity_data.setdefault('temp_coords', []).append([coord_value, 0])
                            elif line == '20' and 'temp_coords' in entity_data:  # Y coordinate
                                if entity_data['temp_coords']:
                                    entity_data['temp_coords'][-1][1] = coord_value
                                    entity_data['coordinates'].append(entity_data['temp_coords'][-1])
                    except ValueError:
                        pass

            i += 1

        # Process final entity
        if current_entity and current_layer in flooring_calculations and entity_data:
            process_flooring_entity(entity_data, flooring_calculations, current_layer)

    except Exception as e:
        log_error(f"Error calculating flooring layers: {str(e)}")

    return flooring_calculations

def process_flooring_entity(entity_data, flooring_calculations, layer_name):
    """Process flooring entity and calculate rectangle dimensions"""
    try:
        if entity_data['type'] in ['LWPOLYLINE', 'POLYLINE'] and len(entity_data['coordinates']) >= 3:
            coords = entity_data['coordinates']

            # Calculate bounding box dimensions
            x_coords = [coord[0] for coord in coords]
            y_coords = [coord[1] for coord in coords]

            length = max(x_coords) - min(x_coords)
            breadth = max(y_coords) - min(y_coords)

            # Calculate area using shoelace formula
            area = 0
            n = len(coords)
            for i in range(n):
                j = (i + 1) % n
                area += coords[i][0] * coords[j][1]
                area -= coords[j][0] * coords[i][1]
            area = abs(area) / 2.0

            if area > 0:
                rectangle_id = f"RECT_{len(flooring_calculations[layer_name]['rectangles']) + 1}"
                flooring_calculations[layer_name]['rectangles'].append({
                    'id': rectangle_id,
                    'length': round(length, 2),
                    'breadth': round(breadth, 2),
                    'area': round(area, 2),
                    'coordinates': coords
                })
                flooring_calculations[layer_name]['total_area'] += area

    except Exception as e:
        log_error(f"Error processing flooring entity: {str(e)}")

def main():
    """Main entry point"""
    if len(sys.argv) != 2:
        result = {
            'success': False,
            'message': 'Usage: python3 process_dxf.py <dxf_file_path>'
        }
        print(json.dumps(result))
        sys.exit(1)
    
    file_path = sys.argv[1]
    result = process_dxf_file(file_path)
    
    # Output result as JSON
    print(json.dumps(result, indent=2))

if __name__ == '__main__':
    main()
