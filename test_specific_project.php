<?php
require_once 'project_manager.php';

// Get specific project
$projectId = $_GET['project'] ?? 'proj_68750d33be667';
$projectManager = new ProjectManager();
$project = $projectManager->getProject($projectId);

if (!$project) {
    die('Project not found: ' . $projectId);
}

$data = $project['estimate_data']['data'] ?? [];
$info = $data['info'] ?? [];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project <?php echo $projectId; ?> - Floor Data Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .floor-section { background-color: #f8f9fa; margin: 10px 0; padding: 10px; border-radius: 3px; }
        .ground-floor { border-left: 4px solid #28a745; }
        .first-floor { border-left: 4px solid #007bff; }
        pre { background-color: #f4f4f4; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        h2 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 5px; }
        h3 { color: #666; margin-top: 15px; }
        .summary { background-color: #e9ecef; padding: 10px; border-radius: 3px; margin: 10px 0; }
        .error { background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Project <?php echo $projectId; ?> - Floor Data Analysis</h1>
    <p><strong>Project:</strong> <?php echo htmlspecialchars($project['client_name'] ?? 'Unknown'); ?></p>
    <p><strong>DXF File:</strong> <?php echo htmlspecialchars($project['dxf_file'] ?? 'Unknown'); ?></p>

    <div class="section">
        <h2>Floor Boundaries</h2>
        <?php if (isset($info['wall_calculations']['floor_boundaries'])): ?>
            <?php foreach ($info['wall_calculations']['floor_boundaries'] as $floorName => $boundary): ?>
                <div class="floor-section <?php echo strtolower(str_replace('_', '-', $floorName)); ?>">
                    <h3><?php echo $floorName; ?></h3>
                    <?php if ($boundary): ?>
                        <p><strong>Type:</strong> <?php echo $boundary['type']; ?></p>
                        <p><strong>Coordinates:</strong></p>
                        <pre><?php echo json_encode($boundary['coordinates'], JSON_PRETTY_PRINT); ?></pre>
                        
                        <?php
                        // Calculate boundary area for reference
                        $coords = $boundary['coordinates'];
                        if (count($coords) >= 3) {
                            $area = 0;
                            $n = count($coords);
                            for ($i = 0; $i < $n; $i++) {
                                $j = ($i + 1) % $n;
                                $area += $coords[$i][0] * $coords[$j][1];
                                $area -= $coords[$j][0] * $coords[$i][1];
                            }
                            $area = abs($area) / 2.0;
                            echo "<p><strong>Boundary Area:</strong> " . number_format($area, 2) . " sq units</p>";
                        }
                        ?>
                    <?php else: ?>
                        <p>No boundary data found</p>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p class="error">No floor boundaries found</p>
        <?php endif; ?>
    </div>

    <div class="section">
        <h2>Layer Areas (MAIN_SLAB Analysis)</h2>
        <?php if (isset($info['layer_areas'])): ?>
            <div class="summary">
                <strong>Raw Layer Areas Data:</strong>
                <pre><?php echo json_encode($info['layer_areas'], JSON_PRETTY_PRINT); ?></pre>
            </div>
            
            <?php foreach (['GROUND_FLOOR', 'FIRST_FLOOR'] as $floorName): ?>
                <div class="floor-section <?php echo strtolower(str_replace('_', '-', $floorName)); ?>">
                    <h3><?php echo $floorName; ?></h3>
                    <?php if (isset($info['layer_areas'][$floorName])): ?>
                        <?php $floorAreas = $info['layer_areas'][$floorName]; ?>
                        
                        <?php if (empty($floorAreas)): ?>
                            <p>No layer area data for this floor</p>
                        <?php else: ?>
                            <?php foreach ($floorAreas as $layerName => $layerData): ?>
                                <h4><?php echo $layerName; ?></h4>
                                <?php if (is_array($layerData) && isset($layerData['entities'])): ?>
                                    <p><strong>Total Area:</strong> <?php echo number_format($layerData['total_area'] ?? 0, 2); ?></p>
                                    <p><strong>Entities:</strong> <?php echo count($layerData['entities']); ?></p>
                                    <pre><?php echo json_encode($layerData, JSON_PRETTY_PRINT); ?></pre>
                                <?php elseif (is_numeric($layerData)): ?>
                                    <p><strong>Area:</strong> <?php echo number_format($layerData, 2); ?></p>
                                <?php else: ?>
                                    <pre><?php echo json_encode($layerData, JSON_PRETTY_PRINT); ?></pre>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    <?php else: ?>
                        <p>No layer area data for this floor</p>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p class="error">No layer areas found</p>
        <?php endif; ?>
    </div>

    <div class="section">
        <h2>Layer Entities (Raw DXF Data)</h2>
        <?php if (isset($info['layer_entities'])): ?>
            <div class="summary">
                <strong>Available Layers:</strong>
                <?php 
                $mainSlabLayers = [];
                foreach ($info['layer_entities'] as $layer => $entities) {
                    if (strpos($layer, 'MAIN_SLAB') !== false) {
                        $mainSlabLayers[$layer] = $entities;
                    }
                }
                ?>
                <pre><?php echo json_encode(array_keys($info['layer_entities']), JSON_PRETTY_PRINT); ?></pre>
            </div>
            
            <?php if (!empty($mainSlabLayers)): ?>
                <h3>MAIN_SLAB Related Layers</h3>
                <pre><?php echo json_encode($mainSlabLayers, JSON_PRETTY_PRINT); ?></pre>
            <?php else: ?>
                <p class="error">No MAIN_SLAB layers found in layer_entities</p>
            <?php endif; ?>
        <?php else: ?>
            <p class="error">No layer entities found</p>
        <?php endif; ?>
    </div>

    <div class="section">
        <h2>Current Estimate Display Logic Test</h2>
        <?php
        // Test the current logic from cpwd_estimate.php
        $mainslabLayers = ['MAIN_SLAB_TOTAL', 'MAIN_SLAB_DEDUCTION'];
        $layerAreas = $info['layer_areas'] ?? [];
        
        echo "<h3>Testing Main Slab Logic:</h3>";
        
        foreach ($mainslabLayers as $layerName) {
            echo "<h4>Testing $layerName:</h4>";
            
            // Check each floor for this layer
            foreach (['GROUND_FLOOR', 'FIRST_FLOOR'] as $floorKey) {
                echo "<p><strong>Checking $floorKey:</strong></p>";
                
                if (isset($layerAreas[$floorKey][$layerName])) {
                    $layerData = $layerAreas[$floorKey][$layerName];
                    echo "<div class='success'>Found $layerName on $floorKey</div>";
                    
                    // Get actual area from DXF calculation
                    $actualArea = 0;
                    if (is_array($layerData) && isset($layerData['entities'])) {
                        // For SUNSHADE type layers with entities
                        foreach ($layerData['entities'] as $entity) {
                            $actualArea += $entity['area'] ?? 0;
                        }
                        echo "<p>Area from entities: " . number_format($actualArea, 2) . "</p>";
                    } elseif (is_numeric($layerData)) {
                        // For simple numeric area values
                        $actualArea = $layerData;
                        echo "<p>Direct area value: " . number_format($actualArea, 2) . "</p>";
                    }
                    
                    if ($actualArea > 0) {
                        $defaultDepth = 0.13;
                        $qty = $actualArea * $defaultDepth;
                        $isDeduction = strpos($layerName, 'DEDUCTION') !== false;
                        $floorDisplayName = ($floorKey === 'GROUND_FLOOR') ? 'Ground Floor' : 'First Floor';
                        $displayName = str_replace(['_', 'MAIN', 'SLAB'], [' ', 'Main', 'Slab'], $layerName) . ' - ' . $floorDisplayName;
                        
                        echo "<div class='success'>";
                        echo "<p><strong>Display Name:</strong> $displayName</p>";
                        echo "<p><strong>Area:</strong> " . number_format($actualArea, 2) . "</p>";
                        echo "<p><strong>Quantity:</strong> " . number_format($qty, 4) . "</p>";
                        echo "<p><strong>Is Deduction:</strong> " . ($isDeduction ? 'Yes' : 'No') . "</p>";
                        echo "</div>";
                    }
                } else {
                    echo "<p>No $layerName found on $floorKey</p>";
                }
            }
        }
        ?>
    </div>

    <div style="margin-top: 30px; text-align: center;">
        <a href="cpwd_estimate.php?project=<?php echo $projectId; ?>" style="padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">View Estimate</a>
        <a href="index.php" style="padding: 10px 20px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 5px; margin-left: 10px;">Back to Projects</a>
    </div>
</body>
</html>
