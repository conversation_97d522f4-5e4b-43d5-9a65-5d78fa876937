<?php
require_once 'project_manager.php';

// Get the latest project for testing
$projectManager = new ProjectManager();
$projects = $projectManager->getAllProjects();

if (empty($projects)) {
    die('No projects found. Please upload a DXF file first.');
}

// Get the most recent project
$latestProject = end($projects);
$data = $latestProject['estimate_data']['data'] ?? [];
$info = $data['info'] ?? [];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Floor Grouping Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .floor-section { background-color: #f8f9fa; margin: 10px 0; padding: 10px; border-radius: 3px; }
        .ground-floor { border-left: 4px solid #28a745; }
        .first-floor { border-left: 4px solid #007bff; }
        pre { background-color: #f4f4f4; padding: 10px; border-radius: 3px; overflow-x: auto; }
        h2 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 5px; }
        h3 { color: #666; margin-top: 15px; }
        .summary { background-color: #e9ecef; padding: 10px; border-radius: 3px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Floor Grouping Test Results</h1>
    <p><strong>Project:</strong> <?php echo htmlspecialchars($latestProject['title'] ?? 'Unknown'); ?></p>
    <p><strong>File:</strong> <?php echo htmlspecialchars($latestProject['dxf_file'] ?? 'Unknown'); ?></p>

    <div class="section">
        <h2>Floor Boundaries</h2>
        <?php if (isset($info['wall_calculations']['floor_boundaries'])): ?>
            <?php foreach ($info['wall_calculations']['floor_boundaries'] as $floorName => $boundary): ?>
                <div class="floor-section <?php echo strtolower(str_replace('_', '-', $floorName)); ?>">
                    <h3><?php echo $floorName; ?></h3>
                    <?php if ($boundary): ?>
                        <p><strong>Type:</strong> <?php echo $boundary['type']; ?></p>
                        <p><strong>Coordinates:</strong></p>
                        <pre><?php echo json_encode($boundary['coordinates'], JSON_PRETTY_PRINT); ?></pre>
                    <?php else: ?>
                        <p>No boundary data found</p>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p>No floor boundaries found</p>
        <?php endif; ?>
    </div>

    <div class="section">
        <h2>Deduction Calculations (Floor Grouped)</h2>
        <?php if (isset($info['deduction_calculations'])): ?>
            <?php foreach (['GROUND_FLOOR', 'FIRST_FLOOR'] as $floorName): ?>
                <div class="floor-section <?php echo strtolower(str_replace('_', '-', $floorName)); ?>">
                    <h3><?php echo $floorName; ?></h3>
                    <?php if (isset($info['deduction_calculations'][$floorName])): ?>
                        <?php 
                        $floorDeductions = $info['deduction_calculations'][$floorName];
                        $totalItems = 0;
                        foreach ($floorDeductions as $layer => $data) {
                            $totalItems += $data['count'];
                        }
                        ?>
                        <div class="summary">
                            <strong>Total Items on <?php echo $floorName; ?>:</strong> <?php echo $totalItems; ?>
                        </div>
                        <?php foreach ($floorDeductions as $layerName => $layerData): ?>
                            <?php if ($layerData['count'] > 0): ?>
                                <h4><?php echo $layerName; ?></h4>
                                <p><strong>Count:</strong> <?php echo $layerData['count']; ?></p>
                                <p><strong>Total Area:</strong> <?php echo number_format($layerData['total_area'], 2); ?></p>
                                <p><strong>Default L×D:</strong> <?php echo $layerData['default_L']; ?> × <?php echo $layerData['default_D']; ?></p>
                                <?php if (!empty($layerData['rectangles'])): ?>
                                    <p><strong>Rectangles:</strong></p>
                                    <pre><?php echo json_encode($layerData['rectangles'], JSON_PRETTY_PRINT); ?></pre>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p>No deduction data for this floor</p>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p>No deduction calculations found</p>
        <?php endif; ?>
    </div>

    <div class="section">
        <h2>RCC Calculations (Floor Grouped)</h2>
        <?php if (isset($info['rcc_calculations'])): ?>
            <?php foreach (['GROUND_FLOOR', 'FIRST_FLOOR'] as $floorName): ?>
                <div class="floor-section <?php echo strtolower(str_replace('_', '-', $floorName)); ?>">
                    <h3><?php echo $floorName; ?></h3>
                    <?php if (isset($info['rcc_calculations'][$floorName])): ?>
                        <?php $floorRcc = $info['rcc_calculations'][$floorName]; ?>
                        <div class="summary">
                            <strong>Columns:</strong> <?php echo count($floorRcc['columns'] ?? []); ?> | 
                            <strong>Beams:</strong> <?php echo count($floorRcc['beams'] ?? []); ?>
                        </div>
                        
                        <?php if (!empty($floorRcc['columns'])): ?>
                            <h4>Columns</h4>
                            <pre><?php echo json_encode($floorRcc['columns'], JSON_PRETTY_PRINT); ?></pre>
                        <?php endif; ?>
                        
                        <?php if (!empty($floorRcc['beams'])): ?>
                            <h4>Beams</h4>
                            <pre><?php echo json_encode($floorRcc['beams'], JSON_PRETTY_PRINT); ?></pre>
                        <?php endif; ?>
                    <?php else: ?>
                        <p>No RCC data for this floor</p>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p>No RCC calculations found</p>
        <?php endif; ?>
    </div>

    <div class="section">
        <h2>Flooring Calculations (Floor Grouped)</h2>
        <?php if (isset($info['flooring_calculations'])): ?>
            <?php foreach (['GROUND_FLOOR', 'FIRST_FLOOR'] as $floorName): ?>
                <div class="floor-section <?php echo strtolower(str_replace('_', '-', $floorName)); ?>">
                    <h3><?php echo $floorName; ?></h3>
                    <?php if (isset($info['flooring_calculations'][$floorName])): ?>
                        <?php $floorFlooring = $info['flooring_calculations'][$floorName]; ?>
                        <?php foreach (['FLOORING_KITCHEN', 'FLOORING_MAIN', 'FLOORING_TOILET'] as $layerName): ?>
                            <?php if (isset($floorFlooring[$layerName]) && !empty($floorFlooring[$layerName]['rectangles'])): ?>
                                <h4><?php echo $layerName; ?></h4>
                                <p><strong>Total Area:</strong> <?php echo number_format($floorFlooring[$layerName]['total_area'], 2); ?></p>
                                <p><strong>Rectangles:</strong> <?php echo count($floorFlooring[$layerName]['rectangles']); ?></p>
                                <pre><?php echo json_encode($floorFlooring[$layerName]['rectangles'], JSON_PRETTY_PRINT); ?></pre>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p>No flooring data for this floor</p>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p>No flooring calculations found</p>
        <?php endif; ?>
    </div>

    <div class="section">
        <h2>Layer Areas (Floor Grouped)</h2>
        <?php if (isset($info['layer_areas'])): ?>
            <?php foreach (['GROUND_FLOOR', 'FIRST_FLOOR'] as $floorName): ?>
                <div class="floor-section <?php echo strtolower(str_replace('_', '-', $floorName)); ?>">
                    <h3><?php echo $floorName; ?></h3>
                    <?php if (isset($info['layer_areas'][$floorName]) && !empty($info['layer_areas'][$floorName])): ?>
                        <pre><?php echo json_encode($info['layer_areas'][$floorName], JSON_PRETTY_PRINT); ?></pre>
                    <?php else: ?>
                        <p>No layer area data for this floor</p>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p>No layer areas found</p>
        <?php endif; ?>
    </div>

    <div style="margin-top: 30px; text-align: center;">
        <a href="index.php" style="padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">Back to Projects</a>
    </div>
</body>
</html>
