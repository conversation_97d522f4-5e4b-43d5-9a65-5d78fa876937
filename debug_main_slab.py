#!/usr/bin/env python3
"""
Debug script to analyze MAIN_SLAB_TOTAL coordinates and floor boundaries
"""

import sys
import json

def point_in_polygon(point, polygon_coords):
    """Check if a point is inside a polygon using ray casting algorithm"""
    try:
        x, y = point
        n = len(polygon_coords)
        inside = False

        p1x, p1y = polygon_coords[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_coords[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside
    except Exception:
        return False

def analyze_main_slab_coordinates(file_path):
    """Analyze MAIN_SLAB_TOTAL coordinates and floor boundaries"""
    
    floor_boundaries = {}
    main_slab_coords = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            lines = file.readlines()

        current_section = None
        current_entity = None
        current_layer = None
        entity_data = None
        i = 0

        while i < len(lines):
            line = lines[i].strip()

            # Track sections
            if line == 'SECTION' and i + 1 < len(lines):
                current_section = lines[i + 1].strip()
            elif line == 'ENDSEC':
                current_section = None

            # Process entities in ENTITIES section
            elif current_section == 'ENTITIES':
                if line == '0' and i + 1 < len(lines):
                    # Save previous entity
                    if current_entity and current_layer and entity_data:
                        if current_layer in ['GROUND_FLOOR', 'FIRST_FLOOR']:
                            floor_boundaries[current_layer] = entity_data
                        elif current_layer == 'MAIN_SLAB_TOTAL':
                            main_slab_coords = entity_data['coordinates']

                    # Start new entity
                    next_line = lines[i + 1].strip()
                    if next_line not in ['ENDSEC', 'EOF']:
                        current_entity = next_line
                        current_layer = None
                        entity_data = {'type': current_entity, 'coordinates': []}

                elif line == '8' and i + 1 < len(lines):  # Layer code
                    current_layer = lines[i + 1].strip()
                    if entity_data:
                        entity_data['layer'] = current_layer

                elif line in ['10', '20'] and i + 1 < len(lines):  # X, Y coordinates
                    try:
                        coord_value = float(lines[i + 1].strip())
                        if entity_data:
                            if line == '10':  # X coordinate
                                entity_data.setdefault('temp_coords', []).append([coord_value, 0])
                            elif line == '20' and 'temp_coords' in entity_data:  # Y coordinate
                                if entity_data['temp_coords']:
                                    entity_data['temp_coords'][-1][1] = coord_value
                                    entity_data['coordinates'].append(entity_data['temp_coords'][-1])
                    except ValueError:
                        pass

            i += 1

        # Process final entity
        if current_entity and current_layer and entity_data:
            if current_layer in ['GROUND_FLOOR', 'FIRST_FLOOR']:
                floor_boundaries[current_layer] = entity_data
            elif current_layer == 'MAIN_SLAB_TOTAL':
                main_slab_coords = entity_data['coordinates']

    except Exception as e:
        print(f"Error reading file: {e}")
        return

    # Analysis
    print("=== MAIN_SLAB_TOTAL COORDINATE ANALYSIS ===")
    print(f"MAIN_SLAB_TOTAL coordinates: {main_slab_coords}")
    
    if main_slab_coords:
        # Calculate center point
        x_coords = [coord[0] for coord in main_slab_coords]
        y_coords = [coord[1] for coord in main_slab_coords]
        center_x = sum(x_coords) / len(x_coords)
        center_y = sum(y_coords) / len(y_coords)
        
        print(f"MAIN_SLAB_TOTAL center point: ({center_x:.2f}, {center_y:.2f})")
        print(f"MAIN_SLAB_TOTAL bounding box: X({min(x_coords):.2f} to {max(x_coords):.2f}), Y({min(y_coords):.2f} to {max(y_coords):.2f})")
        
        # Calculate area
        area = 0
        n = len(main_slab_coords)
        for i in range(n):
            j = (i + 1) % n
            area += main_slab_coords[i][0] * main_slab_coords[j][1]
            area -= main_slab_coords[j][0] * main_slab_coords[i][1]
        area = abs(area) / 2.0
        print(f"MAIN_SLAB_TOTAL area: {area:.2f} sq units")
    else:
        print("No MAIN_SLAB_TOTAL coordinates found!")

    print("\n=== FLOOR BOUNDARIES ANALYSIS ===")
    for floor_name, boundary_data in floor_boundaries.items():
        if boundary_data and 'coordinates' in boundary_data:
            coords = boundary_data['coordinates']
            print(f"\n{floor_name} boundary:")
            print(f"  Coordinates: {coords}")
            
            # Calculate boundary area
            area = 0
            n = len(coords)
            for i in range(n):
                j = (i + 1) % n
                area += coords[i][0] * coords[j][1]
                area -= coords[j][0] * coords[i][1]
            area = abs(area) / 2.0
            print(f"  Area: {area:.2f} sq units")
            
            # Calculate bounding box
            x_coords = [coord[0] for coord in coords]
            y_coords = [coord[1] for coord in coords]
            print(f"  Bounding box: X({min(x_coords):.2f} to {max(x_coords):.2f}), Y({min(y_coords):.2f} to {max(y_coords):.2f})")
            
            # Test if main slab center is within this boundary
            if main_slab_coords:
                center_point = (center_x, center_y)
                is_inside = point_in_polygon(center_point, coords)
                print(f"  MAIN_SLAB center inside {floor_name}: {is_inside}")
        else:
            print(f"{floor_name}: No boundary data")

    print("\n=== RECOMMENDATIONS ===")
    if main_slab_coords and floor_boundaries:
        # Check if main slab is close to any boundary
        for floor_name, boundary_data in floor_boundaries.items():
            if boundary_data and 'coordinates' in boundary_data:
                coords = boundary_data['coordinates']
                x_coords = [coord[0] for coord in coords]
                y_coords = [coord[1] for coord in coords]
                
                # Check if main slab overlaps with boundary bounding box
                main_x_coords = [coord[0] for coord in main_slab_coords]
                main_y_coords = [coord[1] for coord in main_slab_coords]
                
                overlap_x = not (max(main_x_coords) < min(x_coords) or min(main_x_coords) > max(x_coords))
                overlap_y = not (max(main_y_coords) < min(y_coords) or min(main_y_coords) > max(y_coords))
                
                if overlap_x and overlap_y:
                    print(f"- MAIN_SLAB bounding box overlaps with {floor_name} bounding box")
                else:
                    print(f"- MAIN_SLAB bounding box does NOT overlap with {floor_name} bounding box")

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print('Usage: python3 debug_main_slab.py <dxf_file_path>')
        sys.exit(1)
    
    file_path = sys.argv[1]
    analyze_main_slab_coordinates(file_path)
